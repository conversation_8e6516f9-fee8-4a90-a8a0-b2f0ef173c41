using HolyBless.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Buckets
{
    public class Bucket : FullAuditedAggregateRoot<int>
    {
        public int StorageProviderId { get; set; }
        public StorageProvider StorageProvider { get; set; } = default!;
        public string BucketName { get; set; } = default!;
        public string LanguageCode { get; set; } = LangCode.SimplifiedChinese;
        public string SubDomain { get; set; } = default!; //Bucket Url binds to a DNS domain or sub domain
        public ContentCategory ContentType { get; set; } = ContentCategory.Thumbnail;
    }
}